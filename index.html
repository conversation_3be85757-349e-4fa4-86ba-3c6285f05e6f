<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
  <meta name="renderer" content="webkit" />
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
  <link rel="icon" href="/favicon.ico" />



  <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
  <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
  <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <script src="/env.js"></script>
  <title>达翔AI智能体</title>
  <style>
    html,
    body,
    #app {
      height: 100%;
      margin: 0px;
      padding: 0px;
    }

    #x-figure {
      position: absolute;
      margin: auto;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      width: 6.25em;
      height: 6.25em;
      animation: rotate 2.4s linear infinite;
    }

    .x-white {
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      background: white;
      animation: flash 2.4s linear infinite;
      opacity: 0;
    }

    .x-dot {
      position: absolute;
      margin: auto;
      width: 2.4em;
      height: 2.4em;
      border-radius: 100%;
      transition: all 1s ease;
    }

    .x-dot:nth-child(2) {
      top: 0;
      bottom: 0;
      left: 0;
      background: #ff4444;
      animation: dotsY 2.4s linear infinite;
    }

    .x-dot:nth-child(3) {
      left: 0;
      right: 0;
      top: 0;
      background: #ffbb33;
      animation: dotsX 2.4s linear infinite;
    }

    .x-dot:nth-child(4) {
      top: 0;
      bottom: 0;
      right: 0;
      background: #99cc00;
      animation: dotsY 2.4s linear infinite;
    }

    .x-dot:nth-child(5) {
      left: 0;
      right: 0;
      bottom: 0;
      background: #33b5e5;
      animation: dotsX 2.4s linear infinite;
    }

    @keyframes rotate {
      0% {
        transform: rotate(0);
      }

      10% {
        width: 6.25em;
        height: 6.25em;
      }

      66% {
        width: 2.4em;
        height: 2.4em;
      }

      100% {
        transform: rotate(360deg);
        width: 6.25em;
        height: 6.25em;
      }
    }

    @keyframes dotsY {
      66% {
        opacity: 0.1;
        width: 2.4em;
      }

      77% {
        opacity: 1;
        width: 0;
      }
    }

    @keyframes dotsX {
      66% {
        opacity: 0.1;
        height: 2.4em;
      }

      77% {
        opacity: 1;
        height: 0;
      }
    }

    @keyframes flash {
      33% {
        opacity: 0;
        border-radius: 0%;
      }

      55% {
        opacity: 0.6;
        border-radius: 100%;
      }

      66% {
        opacity: 0;
      }
    }
  </style>
</head>

<body>
  <div id="app">
    <div id="x-figure">
      <div class="x-dot x-white"></div>
      <div class="x-dot"></div>
      <div class="x-dot"></div>
      <div class="x-dot"></div>
      <div class="x-dot"></div>
    </div>
  </div>
  <script type="module" src="/src/main.js"></script>
</body>

</html>