/*
 * @Author: 方志良 
 * @Date: 2025-06-06 17:09:53
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-06-25 10:48:33
 * @FilePath: \AI-front\src\api\loginApi.js
 * 
 */
import request from '@/utils/request';
// import { encrypt, decrypt } from '@/utils/rsaUtil';

// 登录方法
export function login(username, password, rememberMe) {
  // const passwordEncrypt = encrypt(password);

  const data = {
    username: username,
    password: password,
  };
  return request({
    url: '/login',
    headers: {
      isToken: false,
    },
    method: 'post',
    data,
  });
}

// 注册方法
export function register(data) {
  return request({
    url: '/register',
    headers: {
      isToken: false,
    },
    method: 'post',
    data,
  });
}

// 退出方法
export function logout() {
  return request({
    url: '/logout',
    method: 'post',
  });
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: '/captchaImage',
    headers: {
      isToken: false,
    },
    method: 'get',
    timeout: 20000,
  });
}

// 获取用户详细信息
export function getLoginUserInfo() {
  return request({
    url: '/getInfo',
    method: 'get'
  })
}