<template>
  <div class="login">
    <div>
      <h2 class="top-title">达翔AI智能体应用平台</h2>
      <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="login-form">
        <h2 class="title">登 录</h2>
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            type="link"
            size="large"
            auto-complete="off"
            placeholder="邮箱"
          >
            <template #prefix>
              <svg-icon icon-class="user" class="el-input__icon input-icon" />
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            size="large"
            auto-complete="off"
            placeholder="密码"
            @keyup.enter="handleLogin"
          >
            <template #prefix>
              <svg-icon icon-class="password" class="el-input__icon input-icon" />
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="code" v-if="isCaptchaOn">
          <el-input
            v-model="loginForm.code"
            size="large"
            auto-complete="off"
            placeholder="验证码"
            style="width: 63%"
            @keyup.enter="handleLogin"
          >
            <template #prefix>
              <svg-icon icon-class="validCode" class="el-input__icon input-icon" />
            </template>
          </el-input>
          <div class="login-code">
            <img :src="codeUrl" @click="getCode" class="login-code-img" />
          </div>
        </el-form-item>
        <el-checkbox v-model="loginForm.rememberMe" style="margin: 0px 0px 25px 0px">
          记住密码
        </el-checkbox>
        <el-form-item style="width: 100%">
          <el-button
            :loading="loading"
            size="large"
            type="primary"
            style="width: 100%"
            @click.prevent="handleLogin"
          >
            <span v-if="!loading">登 录</span>
            <span v-else>登 录 中...</span>
          </el-button>
          <div style="float: right" v-if="register">
            <router-link class="link-type" :to="'/register'">立即注册</router-link>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <!--  底部  -->
    <div class="el-login-footer">
      <span>Copyright © 2025</span>

      <el-link
        href="https://baike.baidu.com/item/%E8%8B%8F%E5%B7%9E%E6%99%AE%E4%B8%AD%E6%99%BA%E8%83%BD%E7%A7%91%E6%8A%80%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8/2542667"
        rel="external nofollow"
        target="_blank"
        type="primary"
      >
        苏州普中智能科技有限公司
      </el-link>

      <span>电话号码:400-086-9986</span>
    </div>
  </div>
</template>

<script setup>
import Cookies from "js-cookie";
import * as loginApi from "@/api/loginApi";
import { encrypt, decrypt } from "@/utils/rsaUtil";

const store = useStore();
const router = useRouter();
const { proxy } = getCurrentInstance();

const loginForm = ref({
  username: "",
  password: "",
  rememberMe: false,
  code: "",
  uuid: "",
});

const loginRules = {
  username: [{ required: true, trigger: "blur", message: "请输入您的账号" }],
  password: [{ required: true, trigger: "blur", message: "请输入您的密码" }],
  code: [{ required: true, trigger: "change", message: "请输入验证码" }],
};

const codeUrl = ref("");
const loading = ref(false);
// 验证码开关
const isCaptchaOn = ref(false);
// 注册开关
const register = ref(false);
const redirect = ref(undefined);
import { ElMessage } from "element-plus";
function handleLogin() {
  proxy.$refs.loginRef.validate((valid) => {
    if (valid) {
      loading.value = true;
      // 勾选了需要记住密码设置在cookie中设置记住用户名和密码
      if (loginForm.value.rememberMe) {
        Cookies.set("username", loginForm.value.username, { expires: 300 });
        Cookies.set("password", encrypt(loginForm.value.password), { expires: 300 });
        Cookies.set("rememberMe", loginForm.value.rememberMe, { expires: 300 });
      } else {
        // 否则移除
        Cookies.remove("username");
        Cookies.remove("password");
        Cookies.remove("rememberMe");
      }
      // 调用action的登录方法
      store
        .dispatch("Login", loginForm.value)
        .then(() => {
          ElMessage({
            message: "操作成功",
            type: "success",
          });
          router.push({ path: redirect.value || "/AIAgent" });
        })
        .catch(() => {
          loading.value = false;
          // 重新获取验证码
          if (isCaptchaOn.value) {
            getCode();
          }
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
}

function getCode() {
  loginApi.getCodeImg().then((res) => {
    isCaptchaOn.value = res.isCaptchaOn === undefined ? true : res.isCaptchaOn;
    if (isCaptchaOn.value) {
      codeUrl.value = `data:image/gif;base64,${res.img}`;
      loginForm.value.uuid = res.uuid;
    }
  });
}

function getCookie() {
  const username = Cookies.get("username");
  const password = Cookies.get("password");
  const rememberMe = Cookies.get("rememberMe");
  loginForm.value = {
    username: username === undefined ? loginForm.value.username : username,
    password: password === undefined ? loginForm.value.password : decrypt(password),
    rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
  };
}

// getCode();
getCookie();
</script>

<style lang="scss" scoped>
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url("../assets/images/background.png");
  background-size: cover;
}
.top-title {
  width: 400px;
  text-align: center;
  color: #ffffff;
  font-size: 35px;
}
.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: black;
}

.login-form {
  // margin-right: 300px;
  border-radius: 6px;
  background: #ffffff;
  width: 400px;
  padding: 25px 25px 5px 25px;

  .el-input {
    height: 40px;

    input {
      height: 40px;
    }
  }

  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 0px;
  }
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.login-code {
  width: 33%;
  height: 40px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #000000;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}

.login-code-img {
  height: 40px;
  padding-left: 12px;
}
</style>
