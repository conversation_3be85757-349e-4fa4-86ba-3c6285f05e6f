<template>
  <div>
    <div class="uploaded-files" v-if="uploadedFiles.length">
      <div class="file-card" v-for="(file, idx) in uploadedFiles" :key="file.id">
        <div class="file-icon">
          <el-icon color="#2775e9" size="30"><Document /></el-icon>
        </div>
        <div class="file-info">
          <div class="file-name">{{ file.name }}</div>
          <div class="file-meta">
            {{ getFileType(file.name) }} {{ formatFileSize(file.size) }}
          </div>
        </div>
        <div class="file-remove" @click="removeFile(idx)">
          <el-icon><CloseBold /></el-icon>
        </div>
      </div>
    </div>
    <!-- 提示词区域 -->
    <div class="prompt-suggestions" v-if="PromptArr.length">
      <div class="prompt-buttons" ref="promptButtonsRef">
        <button
          v-for="(prompt, index) in displayPrompts"
          :key="prompt"
          class="prompt-btn"
          @click="selectPrompt(prompt)"
          :disabled="isStreaming"
        >
          {{ prompt }}
        </button>
        <button
          v-if="showMoreButton"
          class="prompt-btn more-btn"
          @click="toggleShowAll"
          :disabled="isStreaming"
        >
          {{ showAll ? "收起" : `更多(${PromptArr.length - maxVisiblePrompts})` }}
        </button>
      </div>
    </div>

    <div class="chat-input">
      <div class="input-container">
        <textarea
          v-model="inputMessage"
          :disabled="isRecording || isStreaming"
          :placeholder="isStreaming ? 'AI正在回复中...' : '向智能体提问'"
          @keydown.enter.exact.prevent="sendMessage"
        ></textarea>
        <button
          class="file-btn"
          @click="toggleVoiceRecognition"
          :disabled="isStreaming"
          title="上传附件(最多100M)"
        >
          <svg
            class="voice-icon"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 14 20"
            fill="none"
          >
            <path
              d="M7 20c-1.856-.002-3.635-.7-4.947-1.94C.74 16.819.003 15.137 0 13.383V4.828a4.536 4.536 0 0 1 .365-1.843 4.75 4.75 0 0 1 1.087-1.567A5.065 5.065 0 0 1 3.096.368a5.293 5.293 0 0 1 3.888 0c.616.244 1.174.6 1.643 1.05.469.45.839.982 1.088 1.567.25.586.373 1.212.364 1.843v8.555a2.837 2.837 0 0 1-.92 2.027A3.174 3.174 0 0 1 7 16.245c-.807 0-1.582-.3-2.158-.835a2.837 2.837 0 0 1-.92-2.027v-6.22a1.119 1.119 0 1 1 2.237 0v6.22a.777.777 0 0 0 .256.547.868.868 0 0 0 .585.224c.219 0 .429-.08.586-.224a.777.777 0 0 0 .256-.546V4.828A2.522 2.522 0 0 0 7.643 3.8a2.64 2.64 0 0 0-.604-.876 2.816 2.816 0 0 0-.915-.587 2.943 2.943 0 0 0-2.168 0 2.816 2.816 0 0 0-.916.587 2.64 2.64 0 0 0-.604.876 2.522 2.522 0 0 0-.198 1.028v8.555c0 1.194.501 2.339 1.394 3.183A4.906 4.906 0 0 0 7 17.885a4.906 4.906 0 0 0 3.367-1.319 4.382 4.382 0 0 0 1.395-3.183v-6.22a1.119 1.119 0 0 1 2.237 0v6.22c-.002 1.754-.74 3.436-2.052 4.677C10.635 19.3 8.856 19.998 7 20z"
              fill="currentColor"
            ></path>
          </svg>
        </button>

        <!-- 隐藏的文件选择框 -->
        <input
          ref="fileInput"
          type="file"
          style="display: none"
          :accept="acceptTypes"
          @change="handleFileChange"
        />

        <!-- 停止响应按钮 -->
        <button
          v-if="isStreaming"
          class="stop-response-btn"
          @click="stopResponse"
          title="停止响应"
        >
          <i class="fas fa-stop"></i>
        </button>
      </div>
      <button class="send-btn" @click="sendMessage" :disabled="isStreaming">
        <i class="fas fa-paper-plane"></i>
        <span>发送</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed, onMounted, onUnmounted, nextTick } from "vue";
import router from "@/router";
import * as agentListAPi from "@/api/agentApi";
const props = defineProps({
  newMessage: {
    type: String,
    default: "",
  },
  isRecording: {
    type: Boolean,
    default: false,
  },
  isStreaming: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits([
  "send-message",
  "update:newMessage",
  "stop-response",
  "upload-files",
]);
const inputMessage = ref(props.newMessage);
// 选择提示词
const selectPrompt = (prompt) => {
  inputMessage.value = prompt;
  emit("update:newMessage", prompt);
};
let PromptArr = ref([]);
const getAgentTabelApiFn = async () => {
  let res = await agentListAPi.getAgentTabelApi({
    agentId: router.currentRoute.value.query.id,
  });
  PromptArr.value = res.data.map((item) => {
    return item.name;
  });
};
onMounted(() => {
  getAgentTabelApiFn();
});

// 提示词显示控制
const showAll = ref(false);
const promptButtonsRef = ref(null);
const maxVisiblePrompts = ref(999); // 初始设置为一个大数值

const displayPrompts = computed(() => {
  if (showAll.value) {
    return PromptArr.value;
  }
  return PromptArr.value.slice(0, maxVisiblePrompts.value);
});

const showMoreButton = computed(() => {
  return PromptArr.value.length > maxVisiblePrompts.value;
});

const toggleShowAll = () => {
  showAll.value = !showAll.value;
};

// 计算一行能显示多少个提示词
const calculateVisiblePrompts = () => {
  if (PromptArr.value.length === 0) return;

  nextTick(() => {
    const container = promptButtonsRef.value;
    if (!container) return;

    // 临时显示所有按钮来测量
    const originalShowAll = showAll.value;
    const originalMax = maxVisiblePrompts.value;

    showAll.value = true;
    maxVisiblePrompts.value = PromptArr.value.length;

    nextTick(() => {
      const containerWidth = container.offsetWidth;
      const buttons = container.querySelectorAll(".prompt-btn:not(.more-btn)");

      if (buttons.length === 0) return;

      let totalWidth = 0;
      let visibleCount = 0;
      const gap = 8;
      const moreButtonWidth = 80;

      for (let i = 0; i < buttons.length; i++) {
        const buttonWidth = buttons[i].offsetWidth;
        const nextWidth = totalWidth + buttonWidth + (i > 0 ? gap : 0);

        if (
          nextWidth + moreButtonWidth + gap > containerWidth &&
          i < buttons.length - 1
        ) {
          break;
        }

        totalWidth = nextWidth;
        visibleCount = i + 1;
      }

      if (visibleCount < PromptArr.value.length) {
        maxVisiblePrompts.value = visibleCount;
        showAll.value = false;
      } else {
        maxVisiblePrompts.value = PromptArr.value.length;
        showAll.value = originalShowAll;
      }
    });
  });
};

// 监听 PromptArr 变化
watch(
  PromptArr,
  () => {
    if (PromptArr.value.length > 0) {
      calculateVisiblePrompts();
    }
  },
  { deep: true }
);

onMounted(() => {
  calculateVisiblePrompts();
  // 监听窗口大小变化
  window.addEventListener("resize", calculateVisiblePrompts);
});

onUnmounted(() => {
  window.removeEventListener("resize", calculateVisiblePrompts);
});

// 监听外部传入的消息变化
watch(
  () => props.newMessage,
  (newVal) => {
    inputMessage.value = newVal;
  }
);

// 监听内部输入变化，同步到父组件
watch(inputMessage, (newVal) => {
  emit("update:newMessage", newVal);
});

const sendMessage = () => {
  emit("send-message", uploadedFiles.value || []);
  uploadedFiles.value = [];
};

const stopResponse = () => {
  emit("stop-response");
};

// 获取文件类型
const getFileType = (fileName) => {
  const ext = fileName.split(".").pop().toUpperCase();
  return ext || "FILE";
};

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes) return "0B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return Math.round((bytes / Math.pow(k, i)) * 100) / 100 + sizes[i];
};

// 上传文件
const fileInput = ref(null);
const acceptTypes = ".docx,.xlsx";
const toggleVoiceRecognition = () => {
  if (fileInput.value) {
    fileInput.value.click();
  }
};

const uploadedFiles = ref([]);
// 处理文件选择
const handleFileChange = async (event) => {
  const files = event.target.files;
  if (files && files.length > 0) {
    const fileData = new FormData();
    fileData.append("file", files[0]);
    fileData.append("agentId", router.currentRoute.value.query.id);
    let res = await agentListAPi.postFilesApi(fileData, {
      agentId: router.currentRoute.value.query.id,
    });
    uploadedFiles.value = uploadedFiles.value.concat(Array.from([res.data]));
    // 这里可以直接上传，也可以 emit 给父组件处理
    // emit("upload-files", uploadedFiles.value);
    // 清空选择，避免重复选择同一个文件无效
    event.target.value = "";
  }
};

// 删除文件
const removeFile = (idx) => {
  uploadedFiles.value.splice(idx, 1);
  // emit("upload-files", uploadedFiles.value);
};

// 监听 PromptArr 变化
watch(
  PromptArr,
  () => {
    if (PromptArr.value.length > 0) {
      calculateVisiblePrompts();
    }
  },
  { deep: true }
);
</script>

<style lang="scss" scoped>
.chat-input {
  padding: 20px;
  background: white;
  border-top: 1px solid #e4e7ed;
  display: flex;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 5;
}

.uploaded-files {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10px;
  gap: 12px;
}

.file-card {
  background: #ffffff;
  border-radius: 8px;
  padding: 12px;
  display: flex;
  align-items: center;
  min-width: 200px;
  position: relative;
  transition: all 0.3s ease;

  &:hover {
    background: #e8f4fd;

    .file-remove {
      opacity: 1;
    }
  }
}
.file-info {
  flex: 1;
  min-width: 0;
  padding-left: 15px;
}
.file-item {
  background: #f5f7fa;
  border-radius: 8px;
  padding: 4px 10px;
  display: flex;
  align-items: center;
  font-size: 14px;
}
file-icon .file-remove {
  cursor: pointer;
  color: #ff6b6b;
  font-size: 16px;
  margin-left: 2px;
}

.file-meta {
  font-size: 12px;
  color: #666;
}

.file-remove {
  position: absolute;
  top: -6px;
  right: -6px;
  width: 20px;
  height: 20px;
  background: #ff4757;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  opacity: 0;
  transition: all 0.3s ease;
  font-size: 12px;

  &:hover {
    background: #ff3742;
    transform: scale(1.1);
  }
}

.input-container {
  flex-grow: 1;
  position: relative;
}

.chat-input textarea {
  width: 100%;
  border: 1px solid #dcdfe6;
  border-radius: 12px;
  padding: 14px 18px;
  padding-right: 90px; /* 为语音按钮和停止按钮留出空间 */
  resize: none;
  height: 70px;
  font-size: 15px;
  transition: all 0.3s;
}

.chat-input textarea:focus {
  outline: none;
  border-color: #1a2a6c;
  box-shadow: 0 0 0 2px rgba(26, 42, 108, 0.1);
}

.chat-input textarea:disabled {
  background-color: #f5f7fa;
  color: #c0c4cc;
  cursor: not-allowed;
  border-color: #e4e7ed;
}

.file-btn {
  position: absolute;
  right: 15px;
  top: 30%;
  background: transparent;
  border: none;
  color: #1a2a6c;
  font-size: 20px;
  cursor: pointer;
}
.voice-icon {
  width: 24px;
  height: 24px;
  display: inline-block;
  vertical-align: top;
}

.file-btn:hover {
  color: #ff6b6b;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 154, 158, 0.7);
  }

  70% {
    box-shadow: 0 0 0 15px rgba(255, 154, 158, 0);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(255, 154, 158, 0);
  }
}

.send-btn {
  margin-left: 15px;
  background: #4d6bfe;
  border: none;
  color: white;
  border-radius: 12px;
  padding: 0 30px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
  font-size: 16px;
}

.send-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(26, 42, 108, 0.3);
}

.send-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.file-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 停止响应按钮 */
.stop-response-btn {
  position: absolute;
  right: 55px; /* 在语音按钮右边 */
  top: 50%;
  transform: translateY(-50%);
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  border: none;
  color: white;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
  animation: pulse-red 2s infinite;
}

.stop-response-btn:hover {
  transform: translateY(-50%) scale(1.1);
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.5);
}

.stop-response-btn i {
  font-size: 12px;
}

@keyframes pulse-red {
  0% {
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
  }
  50% {
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.6);
  }
  100% {
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
  }
}

/* 提示词样式 */
.prompt-suggestions {
  padding: 12px 20px 0;
  background: white;
}

.prompt-label {
  font-size: 13px;
  color: #666;
  margin-bottom: 8px;
}

.prompt-buttons {
  display: flex;
  gap: 8px;
  overflow: hidden; /* 隐藏超出部分 */
  flex-wrap: wrap;
  padding-bottom: 5px;
}

.prompt-btn {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 20px;
  padding: 6px 16px;
  font-size: 13px;
  color: #495057;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
}

.prompt-btn:hover:not(:disabled) {
  background: #e9ecef;
  border-color: #1a2a6c;
  color: #1a2a6c;
}

.prompt-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.more-btn {
  background: #e3f2fd;
  border-color: #2196f3;
  color: #1976d2;
  font-weight: 500;
}

.more-btn:hover:not(:disabled) {
  background: #bbdefb;
  border-color: #1976d2;
}
</style>
