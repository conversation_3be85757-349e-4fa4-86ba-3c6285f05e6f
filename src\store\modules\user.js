
import * as loginApi from '@/api/loginApi';
import { getToken, setToken, removeToken } from '@/utils/token';
import defAva from '@/assets/images/profile.jpg';

const user = {
  state: {
    token: getToken(),
    permissions: [],
     name: '',
    avatar: '',
    role: '',
     uderState:{}
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token;
    },
     SET_NAME: (state, name) => {
      state.name = name;
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar;
    },
    SET_ROLE: (state, role) => {
      state.role = role;
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions;
    },
    SET_User:(state,data)=>{
      state.uderState=data
    }
  },

  actions: {
    // 登录
    Login({ commit }, userInfo) {
      const username = userInfo.username.trim();
      const { password } = userInfo;
      const { rememberMe } = userInfo;
      return new Promise((resolve, reject) => {
        loginApi
          .login(username, password, rememberMe)
          .then((res) => {
            setToken(res.token);
            commit('SET_TOKEN', res.token);
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

     // 获取用户信息
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        loginApi
          .getLoginUserInfo()
          .then((res) => {
            const { user } = res;
            if(user){
             const avatar =
              user.avatar == '' || user.avatar == null ? defAva : import.meta.env.VITE_APP_BASE_API + user.avatar;
            if (res) {
              // 验证返回的roles是否是一个非空数组
              commit('SET_ROLE', res.roles);
              commit('SET_PERMISSIONS', res.permissions);
            } else {
              commit('SET_ROLE', 'ROLE_DEFAULT');
            }
            commit('SET_NAME', user.nickName);
            commit('SET_AVATAR', avatar);
            commit('SET_User',res.user)
            }
           
            resolve(res);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    // 退出系统
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        loginApi
          .logout(state.token)
          .then(() => {
            commit('SET_TOKEN', '');
            removeToken();
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise((resolve) => {
        commit('SET_TOKEN', '');
        removeToken();
        resolve();
      });
    },
  },
};

export default user;
