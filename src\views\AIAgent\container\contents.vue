<template>
  <div class="contents">
    <h2>智能体集市</h2>

    <!-- <div>
      <el-select
        v-model="addForm.type"
        placeholder="请选择"
        clearable
        filterable
        style="width: 240px"
      >
        <el-option label="全部" value="全部" />
        <el-option label="工作流" value="工作流" />
        <el-option label="聊天助手" value="聊天助手" />
      </el-select>
    </div> -->

    <div class="assistant">
      <div
        class="assistant-item"
        @click="gotoChat(item)"
        v-for="item in agnetList"
        :key="item.id"
      >
        <h2>{{ item.name }}</h2>
        <div class="assistant-item-flex">
          <div class="introduce">
            <div class="text">
              {{ item.description }}
            </div>
            <div class="classify">{{ item.tags }}</div>

            <div class="myinfo">
              <img :src="imgFn(item.avatar)" alt="" class="img-info" />

              <div style="margin: 0 20px">{{ item.updateName }}</div>

              <div>最近编辑 {{ item.updateTime }}</div>
            </div>
          </div>
          <img :src="imgFn(item.icon)" alt="" class="img-app" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import * as agentListAPi from "@/api/agentApi";
const router = useRouter();
const addForm = ref({
  type: "全部",
});

const gotoChat = (row) => {
  localStorage.setItem("agentToken", row.bearerToken);
  localStorage.setItem("agentDescription", row.description);
  router.push({
    path: "/AIAgent/chat",
    query: { id: row.id, name: row.name, icon: row.icon },
  });
};

const imgFn = (val) => {
  if (val) {
    return import.meta.env.VITE_APP_BASE_API + val;
  }
};

let agnetList = ref([]);
const getAgengListFn = async () => {
  let res = await agentListAPi.getAgentListAi();
  agnetList.value = res.data;
};
onMounted(() => {
  getAgengListFn();
});
</script>

<style lang="scss" scoped>
h2 {
  font-weight: 900;
}
.contents {
  padding: 0 20px;

  .assistant {
    margin-top: 20px;
    display: flex;
    flex-wrap: wrap;

    &-item {
      width: calc(50% - 20px);
      // height: 265px;
      margin-bottom: 20px;
      margin-right: 20px;
      border: 2px solid #d1caca;
      border-radius: 10px;
      padding: 20px;
      cursor: pointer;
      box-shadow: 0 10px 50px rgba(0, 0, 0, 0.3);
      background-color: #ffffff;

      &-flex {
        display: flex;
        justify-content: space-between;
        .introduce {
          width: calc(100% - 110px);
          margin-right: 10px;

          .text {
            font-size: 18px;
            color: #999;
          }

          .classify {
            width: 80px;
            height: 30px;
            line-height: 30px;
            text-align: center;
            margin-top: 20px;
            border-radius: 6px;
            font-size: 15px;
            background-color: #edf4fd;
          }

          .myinfo {
            margin-top: 20px;
            display: flex;
            align-items: center;
            color: #999;
            font-size: 14px;

            .img-info {
              width: 30px;
              height: 30px;
              border-radius: 15px;
            }
          }
        }

        .img-app {
          width: 100px;
          height: 100px;
          border-radius: 10px;
        }
      }
    }
  }
}
</style>
