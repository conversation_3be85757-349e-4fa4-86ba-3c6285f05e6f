<template>
  <div>
    <div class="top-header">
      <img :src="imgFn(router.currentRoute.value.query.icon)" alt="" />
      <h3>
        {{ router.currentRoute.value.query.name }}
      </h3>
      <span style="color: #909399">
        {{ description }}
      </span>
    </div>

    <div class="chat-container">
      <!-- 消息列表组件 -->
      <MessageList :current-chat="currentChat" />

      <!-- 聊天输入组件 -->
      <ChatInput
        v-model:new-message="newMessage"
        :is-recording="isRecording"
        :is-streaming="isStreaming"
        @send-message="sendMessage"
        @stop-response="stopResponse"
      />
    </div>

    <!-- 通知组件 -->
    <Notification :notification="notification" @hide-notification="hideNotification" />
  </div>
</template>

<script setup>
import router from "@/router";
import * as agentListAPi from "@/api/agentApi";

import MessageList from "../components/MessageList.vue";

import ChatInput from "../components/ChatInput.vue";

import Notification from "../components/Notification.vue";
let description = ref(localStorage.getItem("agentDescription") || "");

const newMessage = ref("");
const { proxy } = getCurrentInstance();
let filesArr = ref([]);
const uploadFn = (val) => {
  filesArr.value = val.map((item) => {
    return {
      type: "document",
      transfer_method: "local_file",
      upload_file_id: item.id,
    };
  });
};
let conversationId = ref("");

//发送对话信息
const postChatMessageApiFn = async (query, conversation_id) => {
  currentChat.value.messages.push({
    role: "user",
    content: query,
    files: submitFile.value,
  });
  currentChat.value.messages.push({
    role: "ai",
    content: "加载中...",
  });

  // 获取AI消息的索引
  // const aiMessageIndex = currentChat.value.messages.length;

  // 设置流式状态
  isStreaming.value = false;

  try {
    /**
     * 方式一:普通方式，阻塞方式
     */
    let res = await agentListAPi.postChatMessageApi(
      {
        conversation_id: conversation_id,
        inputs: {},
        files: filesArr.value,
        parent_message_id: null,
        query: query,
        response_mode: "blocking",
        user: localStorage.getItem("agentToken"),
      },
      {
        agentId: +router.currentRoute.value.query.id,
      }
    );
    // currentChat.value.messages[currentChat.value.messages.length - 1].content =
    //   res.data.answer;

    const lastIndex = currentChat.value.messages.length - 1;
    currentChat.value.messages[lastIndex] = {
      ...currentChat.value.messages[lastIndex],
      content: res.data.answer,
    };

    conversationId.value = res.data.conversation_id;
    /**
     * 方式二:普通方式，流式模式
     */
    // streamController = agentListAPi.postChatMessageStreamApi(
    //   {
    //     conversation_id,
    //     files: [],
    //     inputs: {},
    //     parent_message_id: null,
    //     query: query,
    //   },
    //   // 消息处理回调
    //   (data) => {
    //     // 逐字追加内容，实现打字效果
    //     currentChat.value.messages[aiMessageIndex - 1].content += data.answer || "";
    //   },
    //   // 完成回调
    //   (data) => {
    //     console.log("流式请求完成", data);
    //     isStreaming.value = false;
    //     streamController = null;

    //     // 如果是用户取消，不需要额外处理
    //     if (data && data.cancelled) {
    //       console.log("用户主动取消响应");
    //       return;
    //     }
    //   },
    //   // 错误回调
    //   (error) => {
    //     console.error("流式请求错误:", error);
    //     isStreaming.value = false;
    //     streamController = null;
    //     currentChat.value.messages[aiMessageIndex - 1].content = "请求失败,请重试...";
    //   }
    // );
  } catch (error) {
    console.error("发送消息失败:", error);
    // isStreaming.value = false;
    streamController = null;
    currentChat.value.messages[currentChat.value.messages.length - 1].content =
      "请求失败,请重试...";
  }
};

// 侧边栏折叠状态
const isCollapsed = ref(false);

// 语音识别状态
const isRecording = ref(false);

// 流式响应状态
const isStreaming = ref(false);
let streamController = null;

// 通知状态
const notification = ref({
  show: false,
  text: "",
  icon: "",
});

// 当前选中的聊天ID

// 当前聊天数据
const currentChat = ref({
  id: "",
  messages: [],
  pinned: false,
  title: "",
});

// 显示通知
const showNotification = (text, icon) => {
  notification.value = {
    show: true,
    text,
    icon,
  };

  setTimeout(() => {
    notification.value.show = false;
  }, 3000);
};
const imgFn = (val) => {
  if (val) {
    return import.meta.env.VITE_APP_BASE_API + val;
  }
};

// 发送消息
let submitFile = ref([]);
const sendMessage = (files) => {
  if (!newMessage.value.trim()) return proxy.$modal.msgError(`输入框不可为空!`);
  // if (!conversationId.value && files.length == 0)
  //  return proxy.$modal.msgError(`首次发送信息需上传文件!`);
  submitFile.value = files;
  uploadFn(files);
  console.log(newMessage.value, "信息");
  postChatMessageApiFn(newMessage.value.trim(), conversationId.value);
  // 清空输入框
  newMessage.value = "";
};

// 隐藏通知
const hideNotification = () => {
  notification.value.show = false;
};

// 停止响应
const stopResponse = () => {
  if (streamController && isStreaming.value) {
    console.log("停止流式响应");
    streamController.close && streamController.close();
    isStreaming.value = false;
    streamController = null;

    // 保留已显示的内容，不添加任何额外信息
    // 用户主动停止，不需要显示错误信息

    showNotification("已停止响应", "fa-stop");
  }
};

// 初始化
onMounted(() => {});
</script>

<style lang="scss" scoped>
.top-header {
  text-align: center;

  img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
  }
}

/* 主聊天区域样式 */
.chat-container {
  height: calc(100vh - 180px);
  display: flex;
  flex-direction: column;
  position: relative;
}
</style>
