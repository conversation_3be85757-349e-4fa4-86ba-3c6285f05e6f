<template>
  <div class="message">
    <div
      class="message-header"
      :style="{ alignSelf: message.role === 'ai' ? 'flex-start' : 'flex-end' }"
    >
      <div class="message-icon">
        <img
          src="@/assets/images/background.jpg"
          v-if="message.role === 'ai'"
          class="role-img"
          alt="AI"
        />
        <img src="@/assets/images/profile.jpg" v-else class="role-img" alt="对话人" />
      </div>
      <div class="message-sender">
        {{
          message.role === "ai"
            ? router.currentRoute.value.query.name
            : store.state.user.name
        }}
      </div>
    </div>
    <div :class="message.role === 'ai' ? 'message-ai' : 'message-user'">
      <div v-if="message.role === 'ai' && type == 'LINK'">
        <!-- https://view.officeapps.live.com/op/embed.aspx?src=https://yjy-teach-oss.oss-cn-beijing.aliyuncs.com/solution/demo.xlsx -->
        <el-button type="primary" @click="downloadExcelFn">点击下载excel</el-button>
        <!-- <el-button type="success" @click="previewExcel" style="margin-left: 10px">
          在线预览
        </el-button> -->
      </div>

      <div
        v-if="message.role === 'ai' && type == 'MARK'"
        v-html="renderMarkdown(fileContent)"
        class="markdown-content"
      ></div>
      <div v-else>
        <div
          v-if="message.role === 'user' && message.files && message.files.length > 0"
          class="user-files"
        >
          <div class="user-file-card" v-for="file in message.files" :key="file.id">
            <div class="file-icon">
              <el-icon color="#2775e9" size="30"><Document /></el-icon>
            </div>
            <div class="file-info">
              <div class="file-name">{{ file.name }}</div>
              <div class="file-meta">
                {{ getFileType(file.name) }} {{ formatFileSize(file.size) }}
              </div>
            </div>
          </div>
        </div>
        <div v-if="contentText">{{ contentText }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import router from "@/router";
import MarkdownIt from "markdown-it";
const store = useStore();
const props = defineProps({
  message: {
    type: Object,
    required: true,
  },
  submitFile: {
    type: Array,
    default: () => [],
  },
});
//LINK、MARK、TEXT、WARN、
let type = ref("");
let contentText = ref("");
let fileContent = ref("");

watchEffect(() => {
  // console.log(
  //   props.message.content.slice(0, 12),
  //   props.message.content.slice(0, 3),
  //   "切割"
  // );
  if (props.message.role == "ai" && props.message.content.slice(0, 3) == "类型：") {
    // console.log(props.message.content.slice(3, 7), "类型");
    console.log(props.message.content, props.message.content.slice(11), "内容");
    type.value = props.message.content.slice(3, 7);
    fileContent.value = props.message.content.slice(11);
    contentText.value = "";
  } else {
    type.value = "";
    contentText.value = props.message.content;
  }
});

const emit = defineEmits(["speak-message", "copy-message"]);
const markDown = new MarkdownIt();

const renderMarkdown = (content) => {
  return markDown.render(content);
};

// const previewExcel = () => {
//   const encodedUrl = encodeURIComponent(fileContent.value);
//   window.open(
//     `https://view.officeapps.live.com/op/view.aspx?src=${encodedUrl}`,
//     "_blank"
//   );
// };

const downloadExcelFn = async () => {
  window.open(fileContent.value, "_blank");
};

// 获取文件类型
const getFileType = (fileName) => {
  const ext = fileName.split(".").pop().toUpperCase();
  return ext || "FILE";
};

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes) return "0B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return Math.round((bytes / Math.pow(k, i)) * 100) / 100 + sizes[i];
};

// 自动滚动到底部
onMounted(() => {
  nextTick(() => {
    const el = document.querySelector(".message:last-child");
    if (el) el.scrollIntoView({ behavior: "smooth" });
  });
});
</script>

<style lang="scss" scoped>
.message {
  max-width: 100%;
  margin-bottom: 10px;
  padding: 18px;
  border-radius: 12px;
  position: relative;
  animation: fadeIn 0.4s ease;

  display: flex;
  flex-direction: column;
  /* 默认左对齐 */
  align-items: flex-start;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(15px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  position: relative;
}

.message-ai .message-header {
  color: #1a2a6c;
}

.message-user .message-header {
  color: rgba(255, 255, 255, 0.9);
}

.message-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  font-size: 16px;
  flex-shrink: 0;

  .role-img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
}

.message-ai .message-icon {
  background: linear-gradient(135deg, #1a2a6c, #2a5298);
  color: white;
}

.message-user .message-icon {
  background: white;
  color: #1a2a6c;
}

.message-sender {
  font-weight: 600;
  font-size: 15px;
}

/* 消息操作按钮 */
.message-actions {
  position: absolute;
  right: 10px;
  bottom: 10px;
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s;
}

.message:hover .message-actions {
  opacity: 1;
}

.message-action-btn {
  background: rgba(0, 0, 0, 0.1);
  border: none;
  color: rgba(0, 0, 0, 0.5);
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
}

.message-user .message-action-btn {
  background: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.8);
}

.message-action-btn:hover {
  background: rgba(0, 0, 0, 0.2);
  color: #1a2a6c;
}

.message-user .message-action-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  color: white;
}

.message-ai {
  align-self: flex-start;
  background: #ffffff;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 10px;

  line-height: 1.7;
  font-size: 16px;
  color: black;
}

.message-user {
  align-self: flex-end;
  background: #cdddff;
  border-radius: 6px;
  padding: 10px;

  line-height: 1.7;
  font-size: 16px;
  color: black;
}

//markdown
.markdown-content {
  line-height: 1.7;
  font-size: 16px;
  color: black;
  max-height: 500px;
  overflow-y: scroll;

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    margin: 16px 0 8px 0;
    font-weight: bold;
  }

  p {
    margin: 8px 0;
  }

  code {
    background-color: #f5f5f5;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
  }

  pre {
    background-color: #f5f5f5;
    padding: 12px;
    border-radius: 6px;
    overflow-x: auto;
    margin: 8px 0;
  }

  ul,
  ol {
    margin: 8px 0;
    padding-left: 20px;
  }

  blockquote {
    border-left: 4px solid #ddd;
    margin: 8px 0;
    padding-left: 16px;
    color: #666;
  }
}

.user-files {
  margin-bottom: 8px;
}

.user-file-card {
  background-color: #ffffff;
  border-radius: 6px;
  padding: 8px;
  display: flex;
  align-items: center;
  margin-bottom: 6px;
  border-radius: 5px;

  .file-icon {
    width: 28px;
    height: 28px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #262626;
    font-size: 14px;
    margin-right: 10px;
    flex-shrink: 0;
  }

  .file-info {
    flex: 1;
    min-width: 0;
  }

  .file-name {
    font-size: 13px;
    color: #262626;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .file-meta {
    font-size: 11px;
    color: #bbbbbb;
  }
}
</style>
