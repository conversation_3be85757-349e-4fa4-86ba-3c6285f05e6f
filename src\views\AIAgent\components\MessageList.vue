<template>
  <div class="chat-messages">
    <template v-if="currentChat && currentChat.messages.length">
      <MessageItem
        v-for="(message, index) in currentChat.messages"
        :key="index"
        :message="message"
      />
    </template>

    <div v-else class="empty-chat">
      <p>请在输入框输入你要提问的问题开始对话</p>
    </div>
  </div>
</template>

<script setup>
import MessageItem from "./MessageItem.vue";

const props = defineProps({
  currentChat: {
    type: Object,
    default: null,
  },
});
</script>

<style lang="scss" scoped>
.chat-messages {
  flex-grow: 1;
  padding: 24px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.empty-chat {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  text-align: center;
  padding: 20px;
}

.empty-chat i {
  font-size: 80px;
  margin-bottom: 20px;
  color: #e4e7ed;
  opacity: 0.7;
}

.empty-chat h2 {
  font-size: 28px;
  margin-bottom: 15px;
  color: #2a5298;
  font-weight: 600;
}

.empty-chat p {
  font-size: 18px;
  max-width: 600px;
  line-height: 1.7;
  margin-bottom: 10px;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.15);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.2);
}
</style>
