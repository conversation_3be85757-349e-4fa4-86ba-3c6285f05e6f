<template>
  <div id="AI-id">
    <!-- 侧边栏组件 -->
    <Sidebar :cachedViews="cachedViews" @send-views="sendViewsFn" />

    <div class="container">
      <section class="home-main">
        <router-view v-slot="{ Component, route }">
          <transition name="fade-transform" mode="out-in">
            <keep-alive :include="cachedViews">
              <component :is="Component" :key="route.path" />
            </keep-alive>
          </transition>
        </router-view>
      </section>
    </div>
  </div>
</template>

<script setup>
import Sidebar from "./Sidebar.vue";
const store = useStore();
const cachedViews = ref([]);
const sendViewsFn = (views) => {
  cachedViews.value.push(views);
};
</script>

<style lang="scss" scoped>
#AI-id {
  width: 100%;
  height: 100%;
  display: flex;
  background: white;
  padding-top: 10px;
  padding-bottom: 10px;
  box-shadow: 0 10px 50px rgba(0, 0, 0, 0.3);

  .container {
    flex: 1;
    background-color: #e4effd;
  }
}
</style>
