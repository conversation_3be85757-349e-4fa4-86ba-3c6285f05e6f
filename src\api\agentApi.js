import request from '@/utils/request';

//智能体列表
export function getAgentListAi(params) {
  return request({
    url: '/agentInfo/list',
    method: 'get',
    params
  });
}

//获取提示词列表
export const getAgentTabelApi=(params)=> {
  return request({
    url: '/agentCue/list',
    method: 'get',
    params
  });
}

//发送对话消息
export function postChatMessageApi(data,params) {
  return request({
    url: '/AgentChat/chat-messages',
    method: 'post',
    data,
    params
  });
}

//发送对话消息 - 流式响应 (使用fetch)
export function postChatMessageStreamApi(params, onMessage, onDone, onError) {
  const baseUrl = import.meta.env.VITE_APP_BASE_API;

  // 构建完整URL
  const url = `${baseUrl}/chat-messages`;

  // 添加固定参数
  const fullParams = {
    ...params,
    user: "app-r7frFiQfVEiuKt8qlUbixPhy",
    response_mode: "streaming"
  };

  // 创建AbortController用于取消请求
  const abortController = new AbortController();

  // 使用fetch API
  fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer app-r7frFiQfVEiuKt8qlUbixPhy'
    },
    body: JSON.stringify(fullParams),
    signal: abortController.signal
  })
  .then(response => {
    console.log(response,'返回的')
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    // 获取响应的reader
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = ''; // 用于缓存不完整的数据

    // 处理数据流
    function processStream() {
      return reader.read().then(({ done, value }) => {
        console.log(value,'二进制')
        if (done) return;

        // 解码二进制数据并添加到缓冲区
        const chunk = decoder.decode(value, { stream: true });
        console.log(chunk,'解码后的数据')
        buffer += chunk;
        // console.log("收到数据块:", chunk);
        console.log("当前缓冲区:", buffer);
        console.log("缓冲区长度:", buffer.length);
        console.log("当前chunk长度:", chunk.length);
        console.log("是否相同:", buffer === chunk);

        // 处理数据块
        try {
          // 按行分割，保留最后一行（可能不完整）
          const lines = buffer.split('\n');
          buffer = lines.pop() || ''; // 保存最后一行到缓冲区

          for (const line of lines) {
            const trimmedLine = line.trim();
            if (trimmedLine && trimmedLine.startsWith('data:')) {
              const jsonStr = trimmedLine.substring(5).trim();

              if (jsonStr === '[DONE]') {
                console.log("收到完成信号");
                onDone && onDone({});
                return;
              }

              if (jsonStr) {
                try {
                  const data = JSON.parse(jsonStr);
                  

                  if (data.event === 'message') {
                    onMessage && onMessage(data);
                  } else if (data.event === 'message_end') {
                    onDone && onDone(data);
                    return;
                  } else if (data.event === 'error') {
                    onError && onError(data);
                    return;
                  }
                } catch (parseError) {
                  console.warn("JSON解析失败，可能是不完整的数据:", jsonStr);
                  // 将失败的数据重新放回缓冲区
                  buffer = trimmedLine + '\n' + buffer;
                }
              }
            }
          }
        } catch (error) {
          console.error("处理数据块错误:", error);
          // 不要立即调用onError，因为这可能只是数据不完整
        }

        // 继续处理流
        return processStream();
      });
    }

    return processStream();
  })
  .catch(error => {
    if (error.name === 'AbortError') {
      console.log("请求已被用户取消");
      // 用户主动取消，调用完成回调而不是错误回调
      onDone && onDone({ cancelled: true });
    } else {
      console.error("fetch请求错误:", error);
      onError && onError(error);
    }
  });

  // 返回一个取消函数
  return {
    close: () => {
      console.log("取消流式请求");
      abortController.abort();
    }
  };
}

//获取历史对话信息
export function getConversationsHistoryApi(data) {
  return request({
    url: '/AgentChat/messages',
    method: 'post',
    data
  });
}

//上传文件
export function postFilesApi(data,params) {
  return request({
    url: '/AgentChat/uploadfile',
    method: 'post',
    // responseType: 'arraybuffer',
    data,
    params,
  });
}


