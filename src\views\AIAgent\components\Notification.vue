<!--
 * @Author: 方志良 
 * @Date: 2025-07-03 14:20:00
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-07-03 14:20:00
 * @FilePath: \AI-front\src\views\AIAgent\components\Notification.vue
 * 
-->
<template>
  <!-- 通知 -->
  <div class="notification" v-if="notification.show" @click="hideNotification">
    <i class="fas" :class="notification.icon"></i>
    <span>{{ notification.text }}</span>
  </div>
</template>

<script setup>
const props = defineProps({
  notification: {
    type: Object,
    default: () => ({
      show: false,
      text: '',
      icon: ''
    })
  }
})

const emit = defineEmits(['hide-notification'])

const hideNotification = () => {
  emit('hide-notification')
}
</script>

<style lang="scss" scoped>
/* 通知 */
.notification {
  position: fixed;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  background: #1a2a6c;
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  gap: 10px;
  z-index: 1000;
  animation: slideUp 0.3s ease;
  cursor: pointer;
}

@keyframes slideUp {
  from {
    bottom: -50px;
    opacity: 0;
  }

  to {
    bottom: 30px;
    opacity: 1;
  }
}

.notification i {
  font-size: 20px;
}
</style>
