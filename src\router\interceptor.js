/*
 * @Author: 方志良
 * @Date: 2025-06-06 17:09:53
 * @LastEditors: 方志良
 * @LastEditTime: 2025-06-25 12:07:29
 * @FilePath: \AI-front\src\router\interceptor.js
 *
 */
import { ElMessage } from "element-plus";
import NProgress from "nprogress";
import router from ".";
import store from "../store";
import "nprogress/nprogress.css";
import { getToken } from "@/utils/token";
import { isReLogin } from '@/utils/request';

NProgress.configure({ showSpinner: false });
const whiteList = ["/login", "/register", "/404", "/401", ];

router.beforeEach((to, from, next) => {
  NProgress.start();
  if (getToken()) {
    if (to.path === "/login") {
      next({ path: "/AIAgent" });
      NProgress.done();
    } 
    else if(!store.getters.name){
       store.dispatch("GetInfo").then(()=>{
        next();
       })
    }  
    else {
      next();
    }
  } else {
    if (whiteList.indexOf(to.path) !== -1) {
      next();
    } else {
      next(`/login?redirect=${to.fullPath}`); // 否则全部重定向到登录页
      NProgress.done();
    }
  }
});

router.afterEach(() => {
  NProgress.done();
});
