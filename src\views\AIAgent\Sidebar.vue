<template>
  <div class="sidebar">
    <img src="@/assets/logo/logo.png" alt="头像" class="ai-logo" />
    <h3>达翔AI</h3>
    <div class="sidebar-menu">
      <div
        v-for="menu in menuList"
        :key="menu.path"
        :class="{ active: isActive(menu.path) }"
        @click="navigateTo(menu.path)"
        class="sidebar-item"
      >
        <img src="@/assets/images/agent.png" alt="智能体图标" />
        <div class="sidebar-title">{{ menu.title }}</div>
      </div>
    </div>

    <div class="logout">
      <el-dropdown
        @command="handleCommand"
        class="right-menu-item hover-effect"
        trigger="click"
      >
        <div class="avatar-wrapper">
          <img :src="store.state.user.avatar" class="out-avatar" />
          <el-icon><caret-bottom /></el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="logout">
              <span>退出登录</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup>
import { ElMessageBox } from "element-plus";
import { useStore } from "vuex";
const store = useStore();
const props = defineProps({
  cachedViews: {
    type: Array,
    default: () => [],
  },
});

const router = useRouter();
const route = useRoute();
// 菜单配置
const menuList = ref([
  {
    path: "/AIAgent/contents",
    title: "智能体",
    // icon: "🤖",
  },
]);

// 判断菜单是否激活
const isActive = (path) => {
  return route.path === path;
};

// 导航到指定路由
const navigateTo = (path) => {
  if (route.path !== path) {
    router.push(path);
  }
};

const emit = defineEmits(["send-views"]);
// 监听路由变化，更新缓存视图
watch(
  () => route.name,
  (newName) => {
    if (newName && !props.cachedViews.includes(newName)) {
      emit("send-views", newName);
    }
  },
  { immediate: true }
);

const handleCommand = () => {
  ElMessageBox.confirm("确定注销并退出系统吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      store.dispatch("LogOut").then(() => {
        location.href = "/AIAgent";
      });
    })
    .catch(() => {});
};
</script>

<style lang="scss" scoped>
.sidebar {
  width: 8%;
  color: #040404;
  margin-left: 10px;
  margin-right: 10px;
  border-radius: 10px;
  padding: 20px 0;
  box-shadow: 2px 0 15px rgba(0, 0, 0, 0.2);
  background: #2775e9;
  display: flex;
  flex-direction: column;
  align-items: center;

  .ai-logo {
    width: 100px;
    height: 50px;
  }
  h3 {
    font-size: 20px;
    color: #ffffff;
  }

  .sidebar-menu {
    margin-top: 20px;
  }

  .sidebar-item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100px;
    height: 100px;
    border-radius: 8px;
    background-color: #3083ff;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background-color: #3083ff;
      color: #409eff;
    }

    &.active {
      background-color: #409eff;
      color: #3083ff;

      // &:hover {
      //   background-color: #3083ff;
      // }
    }
  }

  .sidebar-title {
    margin-top: 10px;
    font-size: 18px;
    text-align: center;
    line-height: 1;
    color: #ffffff;
  }
}

.logout {
  position: absolute;
  bottom: 20px;
  width: 100%;
  text-align: center;
  cursor: pointer;
  color: #666;
  font-size: 12px;
  transition: all 0.3s ease;
}

.out-avatar {
  width: 50px;
  height: 50px;
  border-radius: 15px;
}
</style>
